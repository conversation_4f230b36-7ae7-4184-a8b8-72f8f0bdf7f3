# Frontend Deployment Guide for dev Environment

## Quick Start

1. **Deploy Infrastructure:**
   ```bash
   cd terraform
   terraform apply -target=module.frontend
   ```

2. **Deploy Frontend Code:**
   ```bash
   cd terraform/frontend
   ./deploy-frontend-dev.sh
   ```

## Manual Deployment

If you prefer manual deployment:

1. **Build the frontend:**
   ```bash
   cd frontend
   yarn build:dev
   ```

2. **Deploy to S3:**
   ```bash
   aws s3 sync dist/ s3://nuclear-pond-test-frontend-dev-us-east-1 --delete
   ```

3. **Invalidate CloudFront cache (if CloudFront enabled):**
   ```bash
   # CloudFront not enabled - no cache invalidation needed
   ```

## Configuration

- **Environment:** dev
- **CloudFront:** Disabled
- **S3 Bucket:** nuclear-pond-test-frontend-dev-us-east-1
- **Frontend URL:** http://nuclear-pond-test-frontend-dev-us-east-1.s3-website-us-east-1.amazonaws.com

## Environment Variables

The following environment variables are automatically configured:

- `VITE_API_URL`: http://nuclear-pond-test-dev-alb-1854397662.us-east-1.elb.amazonaws.com
- `VITE_ENVIRONMENT`: dev
- `VITE_API_KEY`: [SENSITIVE]
- `VITE_DEMO_PASSWORD`: [SENSITIVE]

## Troubleshooting

### Build Issues
- Ensure you have Node.js and Yarn installed
- Run `yarn install` to install dependencies
- Check the `.env.dev` file exists

### Deployment Issues
- Verify AWS CLI is configured with appropriate permissions
- Check S3 bucket exists: `aws s3 ls s3://nuclear-pond-test-frontend-dev-us-east-1`
- Verify CloudFront distribution (if enabled): `aws cloudfront get-distribution --id N/A`

### Access Issues
- S3 direct access: http://nuclear-pond-test-frontend-dev-us-east-1.s3-website-us-east-1.amazonaws.com


