import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { scanApi, BackendScanRequest, BackendScanListResponse, BackendScanStatusResponse, BackendScanResultsResponse } from "@/lib/api-client";
import { ScanConfig, ScanRequest, ScanResult } from "../types";

// Choose the appropriate API implementation
const api = scanApi;

// Hook for initiating a scan
export const useInitiateScan = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (config: ScanConfig) => api.initiateScan(config),
    onSuccess: () => {
      // Invalidate relevant queries when a new scan is initiated
      queryClient.invalidateQueries({ queryKey: ['scans'] });
    },
  });
};

// Hook for getting scan status
export const useScanStatus = (scanId: string | undefined) => {
  return useQuery({
    queryKey: ['scanStatus', scanId],
    queryFn: () => scanId ? api.getScanStatus(scanId) : Promise.reject("No scan ID provided"),
    enabled: !!scanId,
    refetchInterval: 5000, // Refetch every 5 seconds for active polling
    refetchIntervalInBackground: false,
  });
};

// Hook for getting scan results
export const useScanResults = (scanId: string | undefined, enabled = true) => {
  return useQuery({
    queryKey: ['scanResult', scanId],
    queryFn: () => scanId ? scanApi.getScanResults(scanId) : Promise.reject("No scan ID provided"),
    enabled: !!scanId && enabled,
  });
};

// Hook for getting all scans with filtering
export const useScans = (options?: { status?: string; limit?: number }) => {
  return useQuery({
    queryKey: ['scans', options],
    queryFn: async () => {
      const response = await scanApi.getAllScans(options);
      
      // Transform backend response to frontend format
      const transformedScans: ScanRequest[] = response.scans.map((backendScan: BackendScanRequest) => ({
        requestId: backendScan.request_id,
        timestamp: backendScan.created_at,
        status: backendScan.status,
        config: {
          targets: backendScan.targets,
          batches: backendScan.batches,
          threads: backendScan.threads,
          output: backendScan.output as 's3',
        },
      }));
      
      return transformedScans;
    },
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute
  });
};
