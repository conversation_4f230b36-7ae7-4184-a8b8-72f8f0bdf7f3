# Nuclear Pond Architecture

This document provides an overview of the Nuclear Pond system architecture, its components, and how different execution modes function.

## Core Concepts

Nuclear Pond is designed as a pure orchestrator for [Nuclei](https://github.com/projectdiscovery/nuclei) scans across various environments. The primary goals of its architecture are scalability, flexibility, and cost-effectiveness through a unified API-based approach.

It achieves this through distinct operational modes:

1.  **Local Execution Mode**: Uses lambda-nuclei-scanner as a subprocess for local scanning via API calls.
2.  **Cloud Execution Mode**: For distributed, highly scalable scanning using AWS Lambda.
3.  **API Service Mode**: Provides an HTTP interface to manage and initiate scans in either local or cloud mode, offering a persistent control plane and state management.

## Component Overview

### 1. Nuclear Pond Server (Pure Orchestrator)

-   **Purpose**: Acts as a pure orchestration layer that delegates all actual scanning work to execution engines.
-   **Commands**:
    -   `nuclearpond local`: Initiates local scans via API calls to lambda-nuclei-scanner subprocess.
    -   `nuclearpond run`: Manages cloud-based scans (AWS Lambda).
    -   `nuclearpond service`: Starts the HTTP API server.
-   **Responsibilities**: Parses user input, prepares targets and Nuclei arguments, makes API calls to execution engines, and handles output aggregation.

### 2. Lambda-Nuclei-Scanner (Execution Engine)

-   **Purpose**: The actual execution engine that runs Nuclei scans.
-   **Dual Mode Operation**:
    -   **Lambda Mode**: Runs as AWS Lambda function for cloud execution.
    -   **CLI Mode**: Runs as standalone executable (`nuclei_local_runner`) for local execution.
-   **Integration**: Contains the Nuclei engine and provides a consistent API interface for both local and cloud execution.

### 3. Local Execution Architecture

-   **Activation**: Used by the `nuclearpond local` command and API server in local mode.
-   **Functionality**:
    -   Nuclear Pond Server starts lambda-nuclei-scanner as a subprocess.
    -   Communicates via API calls to the subprocess.
    -   Manages multiple concurrent subprocess instances for parallelization.
    -   Collects and aggregates results from subprocess API responses.
-   **Dependencies**: Requires `nuclei_local_runner` executable (built from lambda-nuclei-scanner).
-   **Use Case**: Development, small to medium-scale scans, environments without cloud access.

### 4. Cloud Execution Architecture (AWS Lambda)

-   **Activation**: Used by the `nuclearpond run` command and can be invoked via the API server when in `cloud` mode.
-   **Components**:
    -   **AWS Lambda Function**: Runs the same lambda-nuclei-scanner code as local mode but in Lambda environment. Each Lambda invocation runs Nuclei against a batch of targets.
    -   **S3 Bucket**: Used for storing scan results when the `s3` output option is selected. Can also be used to stage large target lists or custom Nuclei templates if needed.
    -   **IAM Roles**: Define permissions for the Lambda function (e.g., to write to S3, CloudWatch Logs) and for the user/tool invoking the Lambda function.
-   **Functionality**:
    -   Nuclear Pond Server prepares batches of targets and Nuclei arguments.
    -   It invokes multiple Lambda functions in parallel using the same API interface as local mode.
    -   Each Lambda function executes lambda-nuclei-scanner with its assigned batch.
    -   Results are returned to Nuclear Pond Server or written to S3.
-   **Use Case**: Large-scale, highly parallelized scanning, serverless operations.

### 5. API Server

-   **Activation**: Started by the `nuclearpond service` command.
-   **Functionality**:
    -   Provides HTTP endpoints for scan management (start scan, get status).
    -   Authenticates requests using a bearer token (`NUCLEARPOND_API_KEY`).
    -   Can initiate scans in either `local` or `cloud` mode based on the API request.
    -   **State Management (DynamoDB)**: When the API server is used, it leverages an AWS DynamoDB table (`AWS_DYNAMODB_TABLE`) to store and track the state of scans (ID, status, targets, results summary, timestamps). This allows for persistent scan tracking even if the API client disconnects.
-   **Dependencies**:
    -   For `local` mode scans via API: Requires `nuclei_local_runner` executable (built from lambda-nuclei-scanner).
    -   For `cloud` mode scans via API: Requires AWS credentials and configuration for Lambda invocation, S3 access, and DynamoDB access.
-   **Use Case**: Programmatic integration, automated workflows, CI/CD pipelines, providing a persistent interface to the scanning capabilities.

## Parameter Flow Architecture

### Batches and Threads Parameters

Nuclear Pond uses two key parameters to control scan execution:

- **Batches (Batch Size)**: Number of targets sent to each Lambda function
- **Threads (Concurrency)**: Number of Lambda functions running simultaneously

### Parameter Processing Flow

```
Frontend UI
    ↓
API Request: { targets: [], batches: N, threads: M }
    ↓
Backend (scanner.go)
    ↓
helpers.SplitSlice(targets, batches) → Creates target groups
    ↓
core.ExecuteScans(batches, output, functionName, nucleiFlags, threads, silent)
    ↓
Goroutine Pool (size = threads)
    ↓
Lambda Invocations (one per batch)
```

### Execution Logic

1. **Target Batching**: `helpers.SplitSlice()` divides targets into groups of size `batches`
2. **Concurrency Control**: `core.ExecuteScans()` creates a goroutine pool of size `threads`
3. **Lambda Distribution**: Each batch is sent to a separate Lambda function
4. **Parallel Execution**: Up to `threads` Lambda functions run concurrently

### Example Calculation

For 100 targets with batches=10 and threads=5:
- Total Lambda invocations: `ceil(100/10) = 10`
- Concurrent Lambdas: `min(10, 5) = 5`
- Execution waves: `ceil(10/5) = 2`

## Data Flow Examples

### Local Scan via CLI

1.  User runs `nuclearpond local -t example.com -a <nuclei_args_base64>`.
2.  Nuclear Pond Server parses arguments and prepares API request.
3.  Nuclear Pond Server starts `nuclei_local_runner` subprocess(es) based on `-c` (threads) and `-b` (batch_size).
4.  Nuclear Pond Server makes API calls to subprocess(es) with target batches and Nuclei arguments.
5.  Each `nuclei_local_runner` subprocess executes Nuclei and returns results via API response.
6.  Nuclear Pond Server aggregates results and streams to standard output.

### Cloud Scan via CLI (to S3)

1.  User runs `nuclearpond run -l targets.txt -a <nuclei_args_base64> -o s3`.
2.  Nuclear Pond Server reads `targets.txt`, batches targets.
3.  For each batch, Nuclear Pond Server invokes the configured AWS Lambda function with targets and Nuclei arguments.
4.  Lambda function executes lambda-nuclei-scanner (same code as local mode).
5.  Lambda-nuclei-scanner runs Nuclei and writes results to S3 or returns them.
6.  S3 paths for results are returned to Nuclear Pond Server and displayed.

### Scan via API Server (Local Mode)

1.  API Client sends `POST /scan` request with `{"targets": ["example.com"], "args": ["..."], "mode": "local"}`.
2.  API Server authenticates, validates, and generates a scan ID.
3.  Scan metadata is written to DynamoDB (status: `queued` or `starting`).
4.  API Server starts `nuclei_local_runner` subprocess(es) and makes API calls with target batches.
5.  Each subprocess executes lambda-nuclei-scanner, which runs Nuclei and returns results.
6.  API Server aggregates results and updates DynamoDB with progress/completion status.
7.  API Client polls `GET /scan/{scan_id}` to get status and results.

### Scan via API Server (Cloud Mode)

1.  API Client sends `POST /scan` request with `{"targets": ["example.com"], "args": ["..."], "mode": "cloud"}`.
2.  API Server authenticates, validates, and generates a scan ID.
3.  Scan metadata is written to DynamoDB (status: `queued` or `starting`).
4.  API Server invokes AWS Lambda functions with target batches.
5.  Lambda functions execute lambda-nuclei-scanner, which runs Nuclei and returns/stores results.
6.  API Server updates DynamoDB with progress/completion status (and S3 location if applicable).
7.  API Client polls `GET /scan/{scan_id}` to get status and results location.

## Infrastructure (Terraform)

-   The AWS infrastructure components (Lambda, S3, DynamoDB, ECS for API backend, IAM roles, etc.) are provisioned and managed using Terraform.
-   Refer to the [Terraform Setup Overview](../../terraform/README.md) and the [Deployment Guide](../../terraform/nuclear_pond_backend/DEPLOYMENT.md) for detailed information on the IaC setup.

## Key Architectural Benefits

This unified architecture provides several advantages:

1. **Consistent API Interface**: Both local and cloud modes use the same lambda-nuclei-scanner execution engine, ensuring consistent behavior and results.

2. **Pure Orchestration**: Nuclear Pond Server acts as a pure orchestrator, delegating all scanning work to specialized execution engines.

3. **Simplified Maintenance**: A single codebase (lambda-nuclei-scanner) handles both local and cloud execution, reducing maintenance overhead.

4. **Scalable Design**: The subprocess-based local execution allows for easy parallelization while maintaining the same API patterns as cloud execution.

5. **Environment Flexibility**: The same scanning logic works seamlessly in both local development environments and cloud production deployments.

This architecture allows Nuclear Pond to be adaptable, from simple local scans to complex, distributed scanning operations managed via an API, while maintaining consistency and simplicity in the underlying execution model.