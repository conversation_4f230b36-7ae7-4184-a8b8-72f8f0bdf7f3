package server

import "time"

// Enhanced scan request structure to store in DynamoDB
type ScanRequest struct {
	ScanID      string     `json:"scan_id"`
	RequestID   string     `json:"request_id"` // Original UUID with dashes
	Status      string     `json:"status"`
	Targets     []string   `json:"targets"`
	Args        string     `json:"args"`
	Mode        string     `json:"mode"`
	Batches     int        `json:"batches"`
	Threads     int        `json:"threads"`
	Output      string     `json:"output"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	TTL         int64      `json:"ttl"`
}

// Scan list response for GET /scans endpoint
type ScanListResponse struct {
	Scans   []ScanRequest `json:"scans"`
	Total   int           `json:"total"`
	Limit   int           `json:"limit"`
	Offset  int           `json:"offset"`
	<PERSON><PERSON><PERSON> bool          `json:"has_more"`
}

// Enhanced scan status response
type ScanStatusResponse struct {
	ScanID      string     `json:"scan_id"`
	RequestID   string     `json:"request_id"`
	Status      string     `json:"status"`
	Config      ScanConfig `json:"config"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	Message     string     `json:"message,omitempty"`
	Error       string     `json:"error,omitempty"`
}

// Scan configuration for response formatting
type ScanConfig struct {
	Targets []string `json:"targets"`
	Batches int      `json:"batches"`
	Threads int      `json:"threads"`
	Output  string   `json:"output"`
	Mode    string   `json:"mode"`
	Args    string   `json:"args"`
}

// Scan results response structure (placeholder for future implementation)
type ScanResultsResponse struct {
	ScanID          string              `json:"scan_id"`
	RequestID       string              `json:"request_id"`
	Status          string              `json:"status"`
	Config          ScanConfig          `json:"config"`
	CreatedAt       time.Time           `json:"created_at"`
	CompletedAt     *time.Time          `json:"completed_at,omitempty"`
	ResultsSummary  *ScanResultsSummary `json:"results_summary,omitempty"`
	ResultsLocation string              `json:"results_location,omitempty"` // S3 path
	Findings        []interface{}       `json:"findings,omitempty"`         // TODO: Define proper structure
}

// Scan results summary
type ScanResultsSummary struct {
	TotalFindings   int `json:"total_findings"`
	CriticalCount   int `json:"critical_count"`
	HighCount       int `json:"high_count"`
	MediumCount     int `json:"medium_count"`
	LowCount        int `json:"low_count"`
	InfoCount       int `json:"info_count"`
	TargetsScanned  int `json:"targets_scanned"`
	DurationSeconds int `json:"duration_seconds"`
}
