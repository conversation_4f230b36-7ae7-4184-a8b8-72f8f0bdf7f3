package server

import (
	"encoding/base64"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"nuclear_pond/pkg/core"
	"nuclear_pond/pkg/helpers"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
)

func backgroundScan(scanInput Request, scanId string) {
	targets := helpers.RemoveEmpty(scanInput.Targets)
	batches := helpers.SplitSlice(targets, scanInput.Batches)
	output := scanInput.Output
	threads := scanInput.Threads
	NucleiArgs := base64.StdEncoding.EncodeToString([]byte(scanInput.Args))
	silent := true

	// Convert scanId to a valid DynamoDB key
	requestId := strings.ReplaceAll(scanId, "-", "")

	log.Println("Initiating scan with the id of ", scanId, "with", len(targets), "targets")

	// Create comprehensive scan request record
	now := time.Now()
	scanRequest := &ScanRequest{
		ScanID:    requestId,
		RequestID: scanId, // Original UUID with dashes
		Status:    "running",
		Targets:   targets,
		Args:      scanInput.Args,
		Mode:      scanInput.Mode,
		Batches:   scanInput.Batches,
		Threads:   scanInput.Threads,
		Output:    scanInput.Output,
		CreatedAt: now,
		UpdatedAt: now,
		TTL:       now.Add(time.Duration(24 * time.Hour)).Unix(), // 24 hour TTL
	}

	// Store comprehensive scan request in DynamoDB
	if err := storeScanRequest(scanRequest); err != nil {
		log.Printf("Failed to store scan request: %v", err)
		// Continue with execution even if storage fails
	}

	storeScanState(requestId, "running")

	// Check if this is a local mode request
	localMode := scanInput.Mode == "local" ||
		os.Getenv("NUCLEARPOND_LOCAL_MODE") == "true" ||
		strings.Contains(strings.ToLower(scanInput.Args), "local") ||
		output == "local"

	if localMode {
		log.Println("Executing scan in local mode using nuclei_local_runner")
		err := executeLocalScanWithSubprocess(targets, scanInput.Args, requestId)
		if err != nil {
			log.Printf("Local scan failed: %v", err)
			updateScanStatus(requestId, "failed")
		} else {
			updateScanStatus(requestId, "completed")
		}
	} else {
		// Cloud mode - use existing Lambda execution
		functionName := os.Getenv("AWS_LAMBDA_FUNCTION_NAME")
		regionName := os.Getenv("AWS_REGION")
		dynamodbTable := os.Getenv("AWS_DYNAMODB_TABLE")
		if functionName == "" || regionName == "" || dynamodbTable == "" {
			log.Fatal("AWS environment variables not set for cloud mode")
		}

		core.ExecuteScans(batches, output, functionName, strings.Split(NucleiArgs, " "), threads, silent)
		updateScanStatus(requestId, "completed")
	}

	log.Println("Scan", scanId, "completed")
}

// executeLocalScanWithSubprocess runs nuclei_local_runner as a subprocess
func executeLocalScanWithSubprocess(targets []string, nucleiArgs string, requestId string) error {
	// Get the path to nuclei_local_runner executable
	nucleiRunnerPath := os.Getenv("NUCLEI_LOCAL_RUNNER_PATH")
	if nucleiRunnerPath == "" {
		// Default to looking for it in the lambda-nuclei-scanner directory
		nucleiRunnerPath = "../lambda-nuclei-scanner/nuclei_local_runner"
		if _, err := os.Stat(nucleiRunnerPath); os.IsNotExist(err) {
			// Try current directory
			nucleiRunnerPath = "./nuclei_local_runner"
			if _, err := os.Stat(nucleiRunnerPath); os.IsNotExist(err) {
				return fmt.Errorf("nuclei_local_runner executable not found. Set NUCLEI_LOCAL_RUNNER_PATH environment variable")
			}
		}
	}

	// Create temporary output file
	tempDir := os.TempDir()
	outputFile := filepath.Join(tempDir, fmt.Sprintf("nuclei_results_%s.json", requestId))
	defer os.Remove(outputFile) // Clean up after use

	// Prepare command arguments
	var cmdArgs []string

	// Add targets
	if len(targets) == 1 {
		cmdArgs = append(cmdArgs, "-targets", targets[0])
	} else {
		cmdArgs = append(cmdArgs, "-targets", strings.Join(targets, ","))
	}

	// Add nuclei arguments if provided
	if nucleiArgs != "" {
		cmdArgs = append(cmdArgs, "-args", nucleiArgs)
	}

	// Add output file
	cmdArgs = append(cmdArgs, "-output-file", outputFile)

	log.Printf("Executing nuclei_local_runner with args: %v", cmdArgs)

	// Execute the subprocess
	cmd := exec.Command(nucleiRunnerPath, cmdArgs...)

	// Capture stdout and stderr
	output, err := cmd.CombinedOutput()

	if err != nil {
		log.Printf("nuclei_local_runner execution failed: %v\nOutput: %s", err, string(output))
		return fmt.Errorf("subprocess execution failed: %v", err)
	}

	log.Printf("nuclei_local_runner completed successfully. Output: %s", string(output))

	// Read results from output file if it exists
	if _, err := os.Stat(outputFile); err == nil {
		results, readErr := os.ReadFile(outputFile)
		if readErr != nil {
			log.Printf("Warning: Could not read results file: %v", readErr)
		} else {
			log.Printf("Scan results: %s", string(results))
			// Here you could store results in DynamoDB or S3 if needed
		}
	} else {
		log.Printf("No results file generated at: %s", outputFile)
	}

	return nil
}

// Update scan status with timestamp
func updateScanStatus(requestId string, status string) error {
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(os.Getenv("AWS_REGION")),
	})
	if err != nil {
		return err
	}

	svc := dynamodb.New(sess)

	// Update scan status and updated_at timestamp
	updateInput := &dynamodb.UpdateItemInput{
		TableName: aws.String(os.Getenv("AWS_DYNAMODB_TABLE")),
		Key: map[string]*dynamodb.AttributeValue{
			"scan_id": {
				S: aws.String(requestId),
			},
		},
		UpdateExpression: aws.String("SET #status = :status, updated_at = :updated_at"),
		ExpressionAttributeNames: map[string]*string{
			"#status": aws.String("status"),
		},
		ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
			":status": {
				S: aws.String(status),
			},
			":updated_at": {
				S: aws.String(time.Now().Format(time.RFC3339)),
			},
		},
	}

	// Add completed_at if status is completed or failed
	if status == "completed" || status == "failed" {
		updateInput.UpdateExpression = aws.String("SET #status = :status, updated_at = :updated_at, completed_at = :completed_at")
		updateInput.ExpressionAttributeValues[":completed_at"] = &dynamodb.AttributeValue{
			S: aws.String(time.Now().Format(time.RFC3339)),
		}
	}

	_, err = svc.UpdateItem(updateInput)
	if err != nil {
		log.Printf("Failed to update scan status in DynamoDB: %v", err)
		return err
	}

	return nil
}

// Retrieve all scans from DynamoDB with pagination
func getAllScans(limit int, exclusiveStartKey map[string]*dynamodb.AttributeValue, statusFilter string) (*ScanListResponse, error) {
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(os.Getenv("AWS_REGION")),
	})
	if err != nil {
		return nil, err
	}

	svc := dynamodb.New(sess)

	input := &dynamodb.ScanInput{
		TableName: aws.String(os.Getenv("AWS_DYNAMODB_TABLE")),
		Limit:     aws.Int64(int64(limit)),
	}

	if exclusiveStartKey != nil {
		input.ExclusiveStartKey = exclusiveStartKey
	}

	// Add status filter if provided
	if statusFilter != "" && statusFilter != "all" {
		input.FilterExpression = aws.String("#status = :status")
		input.ExpressionAttributeNames = map[string]*string{
			"#status": aws.String("status"),
		}
		input.ExpressionAttributeValues = map[string]*dynamodb.AttributeValue{
			":status": {
				S: aws.String(statusFilter),
			},
		}
	}

	result, err := svc.Scan(input)
	if err != nil {
		return nil, err
	}

	// Parse scan results
	scans := make([]ScanRequest, 0, len(result.Items))
	for _, item := range result.Items {
		scan, parseErr := parseScanFromDynamoDB(item)
		if parseErr != nil {
			log.Printf("Failed to parse scan from DynamoDB: %v", parseErr)
			continue
		}
		scans = append(scans, *scan)
	}

	return &ScanListResponse{
		Scans:   scans,
		Total:   len(scans),
		Limit:   limit,
		HasMore: result.LastEvaluatedKey != nil,
	}, nil
}

// Retrieve a single scan by ID
func getScanByID(scanId string) (*ScanRequest, error) {
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(os.Getenv("AWS_REGION")),
	})
	if err != nil {
		return nil, err
	}

	svc := dynamodb.New(sess)

	result, err := svc.GetItem(&dynamodb.GetItemInput{
		TableName: aws.String(os.Getenv("AWS_DYNAMODB_TABLE")),
		Key: map[string]*dynamodb.AttributeValue{
			"scan_id": {
				S: aws.String(scanId),
			},
		},
	})

	if err != nil {
		return nil, err
	}

	if result.Item == nil {
		return nil, fmt.Errorf("scan not found")
	}

	return parseScanFromDynamoDB(result.Item)
}

// Parse DynamoDB item into ScanRequest struct
func parseScanFromDynamoDB(item map[string]*dynamodb.AttributeValue) (*ScanRequest, error) {
	scan := &ScanRequest{}

	if val, exists := item["scan_id"]; exists && val.S != nil {
		scan.ScanID = *val.S
	}

	if val, exists := item["request_id"]; exists && val.S != nil {
		scan.RequestID = *val.S
	}

	if val, exists := item["status"]; exists && val.S != nil {
		scan.Status = *val.S
	}

	if val, exists := item["targets"]; exists && val.L != nil {
		targets := make([]string, len(val.L))
		for i, target := range val.L {
			if target.S != nil {
				targets[i] = *target.S
			}
		}
		scan.Targets = targets
	}

	if val, exists := item["args"]; exists && val.S != nil {
		scan.Args = *val.S
	}

	if val, exists := item["mode"]; exists && val.S != nil {
		scan.Mode = *val.S
	}

	if val, exists := item["batches"]; exists && val.N != nil {
		if batches, err := fmt.Sscanf(*val.N, "%d", &scan.Batches); err != nil || batches != 1 {
			log.Printf("Failed to parse batches: %v", err)
		}
	}

	if val, exists := item["threads"]; exists && val.N != nil {
		if threads, err := fmt.Sscanf(*val.N, "%d", &scan.Threads); err != nil || threads != 1 {
			log.Printf("Failed to parse threads: %v", err)
		}
	}

	if val, exists := item["output"]; exists && val.S != nil {
		scan.Output = *val.S
	}

	if val, exists := item["created_at"]; exists && val.S != nil {
		if createdAt, err := time.Parse(time.RFC3339, *val.S); err == nil {
			scan.CreatedAt = createdAt
		}
	}

	if val, exists := item["updated_at"]; exists && val.S != nil {
		if updatedAt, err := time.Parse(time.RFC3339, *val.S); err == nil {
			scan.UpdatedAt = updatedAt
		}
	}

	if val, exists := item["completed_at"]; exists && val.S != nil {
		if completedAt, err := time.Parse(time.RFC3339, *val.S); err == nil {
			scan.CompletedAt = &completedAt
		}
	}

	if val, exists := item["ttl"]; exists && val.N != nil {
		if ttl, err := fmt.Sscanf(*val.N, "%d", &scan.TTL); err != nil || ttl != 1 {
			log.Printf("Failed to parse TTL: %v", err)
		}
	}

	return scan, nil
}

func storeScanState(requestId string, status string) error {
	log.Println("Stored scan state in Dynamodb", requestId, "as", status)

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(os.Getenv("AWS_REGION")),
	})
	if err != nil {
		return err
	}
	// Create DynamoDB client
	svc := dynamodb.New(sess)

	// Update scan status and updated_at timestamp
	updateInput := &dynamodb.UpdateItemInput{
		TableName: aws.String(os.Getenv("AWS_DYNAMODB_TABLE")),
		Key: map[string]*dynamodb.AttributeValue{
			"scan_id": {
				S: aws.String(requestId),
			},
		},
		UpdateExpression: aws.String("SET #status = :status, updated_at = :updated_at"),
		ExpressionAttributeNames: map[string]*string{
			"#status": aws.String("status"),
		},
		ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
			":status": {
				S: aws.String(status),
			},
			":updated_at": {
				S: aws.String(time.Now().Format(time.RFC3339)),
			},
		},
	}

	// Add completed_at if status is completed or failed
	if status == "completed" || status == "failed" {
		updateInput.UpdateExpression = aws.String("SET #status = :status, updated_at = :updated_at, completed_at = :completed_at")
		updateInput.ExpressionAttributeValues[":completed_at"] = &dynamodb.AttributeValue{
			S: aws.String(time.Now().Format(time.RFC3339)),
		}
	}

	_, err = svc.UpdateItem(updateInput)
	if err != nil {
		log.Println("Failed to update scan state in DynamoDB:", err)
		return err
	}

	return nil
}

// function to retrieve the scan state from DynamoDB
func getScanState(requestId string) (string, error) {
	log.Println("Retrieving scan state from Dynamodb", requestId)

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(os.Getenv("AWS_REGION")),
	})
	if err != nil {
		return "failed", err
	}
	// Create DynamoDB client
	svc := dynamodb.New(sess)
	// Prepare the item to be put into the DynamoDB table
	item := &dynamodb.GetItemInput{
		TableName: aws.String(os.Getenv("AWS_DYNAMODB_TABLE")),
		Key: map[string]*dynamodb.AttributeValue{
			"scan_id": {
				S: aws.String(requestId),
			},
		},
	}
	// Store the item in DynamoDB
	result, err := svc.GetItem(item)
	if err != nil {
		return "failed", err
	}
	return *result.Item["status"].S, nil
}
